using Odrc.Dots.Data.Transfers;
using Odrc.Dots.Entities.Common;
using Odrc.Dots.Entities.Transfers;
using Odrc.Dots.Entities.Transfers.WorkLists;
using System;
using System.Collections.Generic;

namespace Odrc.Dots.Business.Transfers
{
    public class MosciLogic
    {
       public static List<MosciData> GetMosciInfoByOaksId(string oaksId)
        {
            var mosciInfo = DataRepositoryFactory.GetMosciRepository().GetMosciInfoByOaksId(oaksId);
            return mosciInfo;
        }
        public static int InsertorUpdateMosci(string combineOffenderId, string SchDate, int Instno, int SchdInst, string Descrl, string username, string rowId)
        {
            var mosciInfo = DataRepositoryFactory.GetMosciRepository().InsertorUpdateMosci(combineOffenderId, SchDate, Instno,  SchdInst, Descrl, username, rowId);
            return mosciInfo;
        }
        public static int DeleteMosciRecord(string rowId, string username)    
        {
            var mosciInfo = DataRepositoryFactory.GetMosciRepository().DeleteMosciRecord(rowId, username);
            return mosciInfo;
        }
        public static bool CheckIfOffenderExists(string OID, string schdate)
        {
            var mosciInfo = DataRepositoryFactory.GetMosciRepository().CheckIfOffenderExists(OID, schdate);
            return mosciInfo;
        }                              
    }
}