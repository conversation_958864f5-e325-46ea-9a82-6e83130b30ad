using Odrc.Dots.Entities.Common;
using Odrc.Dots.Entities.Common.Lookup;
using Odrc.Dots.Entities.Transfers;
using System;
using System.Collections.Generic;


namespace Odrc.Dots.Data.Transfers.Interfaces
{
    public  interface IMosciRepository
    {
         List<MosciData> GetMosciInfoByOaksId(string oaksId);
        int InsertorUpdateMosci(string combineOffenderId, string SchDate, int Instno, int SchdInst, string Descrl, string username, string rowId);
        int DeleteMosciRecord(string rowId, string username);
        bool CheckIfOffenderExists(string OID, string schdate);

    }
}